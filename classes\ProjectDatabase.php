<?php

require_once 'connectdb.php';

/**
 * ProjectDatabase Class
 * 
 * Handles all database operations for projects.
 * Single responsibility: Database queries only.
 */
class ProjectDatabase 
{
    private $connection;
    
    public function __construct() 
    {
        $database = new Database();
        $this->connection = $database->getConnection();
    }
    
    /**
     * Fetch all active projects with comprehensive metadata
     *
     * Returns projects with manager info, latest assignments for:
     * - Tags, Status, Department, Project Type
     * - User subscription status
     */
    public function getAllActiveProjects()
    {
        $sql = $this->buildProjectQuery();

        $stmt = $this->connection->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Build the main project query with all joins
     * Broken down for readability and maintainability
     */
    private function buildProjectQuery()
    {
        return "SELECT " . $this->getSelectFields() . "
                FROM projects
                " . $this->getManagerJoin() . "
                " . $this->getTagJoins() . "
                " . $this->getStatusJoins() . "
                " . $this->getDepartmentJoins() . "
                " . $this->getTypeJoins() . "
                " . $this->getSubscriptionJoin() . "
                WHERE projects.proj_isactive = 1
                GROUP BY projects.proj_uniqid";
    }

    /**
     * Define all fields to select
     */
    private function getSelectFields()
    {
        return "projects.proj_uniqid,
                projects.proj_initdate,
                projects.proj_startdate,
                projects.proj_enddate,
                projects.proj_name,
                projects.proj_internal,
                users.user_firstname as proj_mgr_firstname,
                users.user_lastname as proj_mgr_lastname,
                users.user_email as proj_mgr_email,
                projects.proj_desc,
                projects.proj_desc_long,
                projects.proj_budget,
                projects.proj_location,
                departments.dept_name,
                project_types.projtypes_name,
                project_tag_assignments_latest.projtagassign_tag_arr AS tag_names,
                latest_status.status_name AS latest_status_name,
                CASE
                    WHEN subscriptions.subscription_isactive = 1 AND projects.proj_uniqid = subscriptions.subscription_proj_uniqid THEN 'Subscribed'
                    ELSE NULL
                END AS subscription_status";
    }

    /**
     * Join for project manager information
     * Handles legacy dual-reference (user_id OR user_uniqid)
     */
    private function getManagerJoin()
    {
        return "LEFT JOIN users ON projects.proj_mgr_userid = users.user_id
                                OR projects.proj_mgr_userid = users.user_uniqid";
    }

    /**
     * Joins for latest tag assignments
     * Uses subquery to get most recent tag assignment per project
     */
    private function getTagJoins()
    {
        return "LEFT JOIN (
                    SELECT projtagassign_proj_id, MAX(projtagassign_datetime) AS max_datetime
                    FROM project_tag_assignments
                    GROUP BY projtagassign_proj_id
                ) AS latest_tag_assignment ON projects.proj_uniqid = latest_tag_assignment.projtagassign_proj_id
                LEFT JOIN project_tag_assignments AS project_tag_assignments_latest
                    ON latest_tag_assignment.projtagassign_proj_id = project_tag_assignments_latest.projtagassign_proj_id
                    AND latest_tag_assignment.max_datetime = project_tag_assignments_latest.projtagassign_datetime";
    }

    /**
     * Joins for latest status assignments
     * Uses MAX(id) to get most recent status per project
     */
    private function getStatusJoins()
    {
        return "LEFT JOIN (
                    SELECT projectstatusassign_proj_id, MAX(projectstatusassign_id) AS max_status_id
                    FROM project_status_assignments
                    GROUP BY projectstatusassign_proj_id
                ) AS latest_status_assignment ON projects.proj_uniqid = latest_status_assignment.projectstatusassign_proj_id
                LEFT JOIN project_status_assignments AS latest_status_assignment_details
                    ON latest_status_assignment.projectstatusassign_proj_id = latest_status_assignment_details.projectstatusassign_proj_id
                    AND latest_status_assignment.max_status_id = latest_status_assignment_details.projectstatusassign_id
                LEFT JOIN statuses AS latest_status
                    ON latest_status_assignment_details.projectstatusassign_status_id = latest_status.status_id";
    }

    /**
     * Joins for latest department assignments
     * Uses MAX(datetime) to get most recent department per project
     */
    private function getDepartmentJoins()
    {
        return "LEFT JOIN (
                    SELECT projdeptassign_proj_id, MAX(projdeptassign_datetime) AS max_datetime
                    FROM project_dept_assignments
                    GROUP BY projdeptassign_proj_id
                ) AS latest_assignment_dept ON projects.proj_uniqid = latest_assignment_dept.projdeptassign_proj_id
                LEFT JOIN project_dept_assignments
                    ON latest_assignment_dept.projdeptassign_proj_id = project_dept_assignments.projdeptassign_proj_id
                    AND latest_assignment_dept.max_datetime = project_dept_assignments.projdeptassign_datetime
                LEFT JOIN departments ON project_dept_assignments.projdeptassign_dept_id = departments.dept_id";
    }

    /**
     * Joins for latest project type assignments
     * Uses MAX(datetime) to get most recent type per project
     */
    private function getTypeJoins()
    {
        return "LEFT JOIN (
                    SELECT projtypeassign_proj_id, MAX(projtypeassign_datetime) AS max_datetime
                    FROM project_type_assignments
                    GROUP BY projtypeassign_proj_id
                ) AS latest_assignment_type ON projects.proj_uniqid = latest_assignment_type.projtypeassign_proj_id
                LEFT JOIN project_type_assignments
                    ON latest_assignment_type.projtypeassign_proj_id = project_type_assignments.projtypeassign_proj_id
                    AND latest_assignment_type.max_datetime = project_type_assignments.projtypeassign_datetime
                LEFT JOIN project_types ON project_type_assignments.projtypeassign_type_id = project_types.projtypes_id";
    }

    /**
     * Join for user subscription status
     */
    private function getSubscriptionJoin()
    {
        return "LEFT JOIN subscriptions ON projects.proj_uniqid = subscriptions.subscription_proj_uniqid";
    }
}
