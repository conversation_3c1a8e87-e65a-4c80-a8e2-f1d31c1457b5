<?php

/**
 * ApiResponse Class
 * 
 * Handles standardized JSON responses.
 * Single responsibility: Response formatting only.
 */
class ApiResponse 
{
    /**
     * Send success response
     */
    public static function success($data, $message = 'Success') 
    {
        self::sendResponse([
            'success' => true,
            'data' => $data,
            'message' => $message
        ]);
    }
    
    /**
     * Send error response
     */
    public static function error($message, $httpCode = 400) 
    {
        http_response_code($httpCode);
        self::sendResponse([
            'success' => false,
            'message' => $message,
            'data' => null
        ]);
    }
    
    /**
     * Send JSON response and exit
     */
    private static function sendResponse($response) 
    {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
}
