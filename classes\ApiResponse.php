<?php

/**
 * ApiResponse Class
 * 
 * Provides standardized JSON response formatting for API endpoints.
 * Ensures consistent response structure across the application and
 * simplifies error handling for frontend consumers.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
class ApiResponse
{
    /**
     * Send a successful response with data
     * 
     * @param mixed $data The data to return
     * @param string $message Optional success message
     * @param int $httpCode HTTP status code (default: 200)
     */
    public static function success($data = null, $message = 'Success', $httpCode = 200)
    {
        http_response_code($httpCode);
        header('Content-Type: application/json');
        
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }
    
    /**
     * Send an error response
     * 
     * @param string $message Error message
     * @param int $httpCode HTTP status code (default: 400)
     * @param mixed $details Optional error details for debugging
     */
    public static function error($message = 'An error occurred', $httpCode = 400, $details = null)
    {
        http_response_code($httpCode);
        header('Content-Type: application/json');
        
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Include details only in development/debug mode
        if ($details !== null && self::isDebugMode()) {
            $response['details'] = $details;
        }
        
        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }
    
    /**
     * Check if application is in debug mode
     * 
     * @return bool
     */
    private static function isDebugMode()
    {
        // This can be configured via environment variables or config files
        return defined('DEBUG_MODE') && DEBUG_MODE === true;
    }
}
