<?php

/**
 * Projects API - Refactored Version
 *
 * Clean, maintainable version of the original fetch_allprojects script.
 * Focuses on separation of concerns, error handling, and team readability.
 *
 * @version 2.0
 */

include 'session_check.php';
require_once 'connectdb.php';

/**
 * Database class for project operations
 */
class ProjectDatabase
{
    private $connection;

    public function __construct()
    {
        $database = new Database();
        $this->connection = $database->getConnection();
    }

    /**
     * Fetch all active projects with metadata
     */
    public function getAllActiveProjects()
    {
        $sql = "SELECT
                    projects.proj_uniqid,
                    projects.proj_initdate,
                    projects.proj_startdate,
                    projects.proj_enddate,
                    projects.proj_name,
                    projects.proj_internal,
                    users.user_firstname as proj_mgr_firstname,
                    users.user_lastname as proj_mgr_lastname,
                    users.user_email as proj_mgr_email,
                    projects.proj_desc,
                    projects.proj_desc_long,
                    projects.proj_budget,
                    projects.proj_location,
                    departments.dept_name,
                    project_types.projtypes_name,
                    project_tag_assignments_latest.projtagassign_tag_arr AS tag_names,
                    latest_status.status_name AS latest_status_name,
                    CASE
                        WHEN subscriptions.subscription_isactive = 1 AND projects.proj_uniqid = subscriptions.subscription_proj_uniqid THEN 'Subscribed'
                        ELSE NULL
                    END AS subscription_status
                FROM projects
                LEFT JOIN users ON projects.proj_mgr_userid = users.user_id OR projects.proj_mgr_userid = users.user_uniqid
                LEFT JOIN (
                    SELECT projtagassign_proj_id, MAX(projtagassign_datetime) AS max_datetime
                    FROM project_tag_assignments
                    GROUP BY projtagassign_proj_id
                ) AS latest_tag_assignment ON projects.proj_uniqid = latest_tag_assignment.projtagassign_proj_id
                LEFT JOIN project_tag_assignments AS project_tag_assignments_latest ON latest_tag_assignment.projtagassign_proj_id = project_tag_assignments_latest.projtagassign_proj_id
                    AND latest_tag_assignment.max_datetime = project_tag_assignments_latest.projtagassign_datetime
                LEFT JOIN (
                    SELECT projectstatusassign_proj_id, MAX(projectstatusassign_id) AS max_status_id
                    FROM project_status_assignments
                    GROUP BY projectstatusassign_proj_id
                ) AS latest_status_assignment ON projects.proj_uniqid = latest_status_assignment.projectstatusassign_proj_id
                LEFT JOIN project_status_assignments AS latest_status_assignment_details ON latest_status_assignment.projectstatusassign_proj_id = latest_status_assignment_details.projectstatusassign_proj_id
                    AND latest_status_assignment.max_status_id = latest_status_assignment_details.projectstatusassign_id
                LEFT JOIN statuses AS latest_status ON latest_status_assignment_details.projectstatusassign_status_id = latest_status.status_id
                LEFT JOIN (
                    SELECT projdeptassign_proj_id, MAX(projdeptassign_datetime) AS max_datetime
                    FROM project_dept_assignments
                    GROUP BY projdeptassign_proj_id
                ) AS latest_assignment_dept ON projects.proj_uniqid = latest_assignment_dept.projdeptassign_proj_id
                LEFT JOIN project_dept_assignments ON latest_assignment_dept.projdeptassign_proj_id = project_dept_assignments.projdeptassign_proj_id
                    AND latest_assignment_dept.max_datetime = project_dept_assignments.projdeptassign_datetime
                LEFT JOIN departments ON project_dept_assignments.projdeptassign_dept_id = departments.dept_id
                LEFT JOIN (
                    SELECT projtypeassign_proj_id, MAX(projtypeassign_datetime) AS max_datetime
                    FROM project_type_assignments
                    GROUP BY projtypeassign_proj_id
                ) AS latest_assignment_type ON projects.proj_uniqid = latest_assignment_type.projtypeassign_proj_id
                LEFT JOIN project_type_assignments ON latest_assignment_type.projtypeassign_proj_id = project_type_assignments.projtypeassign_proj_id
                    AND latest_assignment_type.max_datetime = project_type_assignments.projtypeassign_datetime
                LEFT JOIN project_types ON project_type_assignments.projtypeassign_type_id = project_types.projtypes_id
                LEFT JOIN subscriptions ON projects.proj_uniqid = subscriptions.subscription_proj_uniqid
                WHERE projects.proj_isactive = 1
                GROUP BY projects.proj_uniqid";

        $stmt = $this->connection->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}

/**
 * Process project data for frontend consumption
 */
function processProjectData($projects)
{
    $processedProjects = [];

    foreach ($projects as $project) {
        // Handle JSON location data
        if (!empty($project['proj_location']) && strpos($project['proj_location'], '[') === 0) {
            $project['proj_location'] = json_decode($project['proj_location'], true);
        }

        // Convert tag string to array
        if (!empty($project['tag_names'])) {
            $project['tag_names'] = array_filter(explode(',', $project['tag_names']));
        } else {
            $project['tag_names'] = [];
        }

        $processedProjects[] = $project;
    }

    return $processedProjects;
}

/**
 * Send JSON response
 */
function sendResponse($data, $success = true, $message = '')
{
    header('Content-Type: application/json');

    $response = [
        'success' => $success,
        'data' => $data
    ];

    if ($message) {
        $response['message'] = $message;
    }

    echo json_encode($response);
    exit;
}

// Main execution
try {
    // Check authentication
    if (!isset($_SESSION['user_id'])) {
        sendResponse(null, false, 'User not logged in');
    }

    // Get projects
    $projectDb = new ProjectDatabase();
    $rawProjects = $projectDb->getAllActiveProjects();
    $projects = processProjectData($rawProjects);

    // Send success response
    sendResponse($projects, true, 'Projects retrieved successfully');

} catch (Exception $e) {
    // Log error and send user-friendly response
    error_log("Project API Error: " . $e->getMessage());
    sendResponse(null, false, 'Unable to retrieve projects. Please try again.');
}

?>
