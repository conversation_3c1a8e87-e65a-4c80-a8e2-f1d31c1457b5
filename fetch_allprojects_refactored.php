<?php

/**
 * Projects API - Refactored Version
 *
 * Clean, maintainable version with proper separation of concerns.
 * Each class has a single responsibility for better maintainability.
 *
 * Architecture Choice: Object-Oriented with Clear Separation
 * - ProjectDatabase: Handles all database operations
 * - ProjectProcessor: Handles data transformation and formatting
 * - ApiResponse: Handles standardized JSON responses
 * - Main file: Simple orchestration and error handling
 *
 * This approach provides clear separation of responsibilities,
 * making the code easier to test, maintain, and extend.
 *
 * @version 2.0
 */

include 'session_check.php';
require_once 'classes/ProjectDatabase.php';
require_once 'classes/ProjectProcessor.php';
require_once 'classes/ApiResponse.php';

// Main execution
try {
    // Check authentication
    if (!isset($_SESSION['user_id'])) {
        ApiResponse::error('User not logged in', 401);
    }

    // Initialize components
    $database = new ProjectDatabase();
    $processor = new ProjectProcessor();

    // Get and process projects
    $rawProjects = $database->getAllActiveProjects();
    $processedProjects = $processor->processProjectData($rawProjects);

    // Send success response
    ApiResponse::success($processedProjects, 'Projects retrieved successfully');

} catch (Exception $e) {
    // Log error and send user-friendly response
    error_log("Project API Error: " . $e->getMessage());
    ApiResponse::error('Unable to retrieve projects. Please try again.', 500);
}

?>
