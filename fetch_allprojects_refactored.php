<?php

/**
 * Projects API Endpoint - Refactored Version
 * 
 * Retrieves comprehensive active project data with associated metadata.
 * This endpoint provides project information including manager details,
 * departments, types, tags, status, and subscription data.
 * 
 * Features:
 * - Clean separation of concerns with dedicated classes
 * - Standardized error handling and response formatting
 * - Enhanced data processing and validation
 * - Team-friendly modular architecture
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since 2024-06-13
 */

// Include dependencies
include 'session_check.php';
require_once 'classes/ApiResponse.php';
require_once 'classes/ProjectService.php';

/**
 * Main endpoint logic
 */
try {
    // Validate user session
    if (!isset($_SESSION['user_id'])) {
        ApiResponse::error('Authentication required. Please log in to access this resource.', 401);
    }
    
    // Initialize service layer
    $projectService = new ProjectService();
    
    // Fetch and process project data
    $projects = $projectService->getAllActiveProjects();
    
    // Return successful response
    ApiResponse::success($projects, 'Projects retrieved successfully');
    
} catch (Exception $e) {
    // Log error for debugging (in production, use proper logging)
    error_log("Project API Error: " . $e->getMessage());
    
    // Return user-friendly error response
    ApiResponse::error(
        'Unable to retrieve projects at this time. Please try again later.',
        500,
        $e->getMessage()
    );
}

?>
