<?php

/**
 * ProjectProcessor Class
 * 
 * Handles data processing and formatting for projects.
 * Single responsibility: Data transformation only.
 */
class ProjectProcessor 
{
    /**
     * Process project data for frontend consumption
     */
    public function processProjectData($projects) 
    {
        $processedProjects = [];
        
        foreach ($projects as $project) {
            $processedProjects[] = $this->processIndividualProject($project);
        }
        
        return $processedProjects;
    }
    
    /**
     * Process individual project record
     */
    private function processIndividualProject($project) 
    {
        // Handle JSON location data
        if (!empty($project['proj_location']) && strpos($project['proj_location'], '[') === 0) {
            $project['proj_location'] = json_decode($project['proj_location'], true);
        }
        
        // Convert tag string to array
        if (!empty($project['tag_names'])) {
            $project['tag_names'] = array_filter(explode(',', $project['tag_names']));
        } else {
            $project['tag_names'] = [];
        }
        
        return $project;
    }
}
