<?php

require_once 'classes/ProjectRepository.php';

/**
 * ProjectService Class
 * 
 * Handles business logic and data processing for project operations.
 * Acts as an intermediary between the repository layer and the API endpoints,
 * applying business rules and data transformations.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
class ProjectService
{
    private $projectRepository;
    
    /**
     * Constructor - Initialize dependencies
     */
    public function __construct()
    {
        $this->projectRepository = new ProjectRepository();
    }
    
    /**
     * Get all active projects with processed data
     * 
     * Retrieves projects from repository and applies business logic
     * such as data formatting, validation, and enrichment.
     * 
     * @return array Processed project data
     * @throws Exception If data retrieval or processing fails
     */
    public function getAllActiveProjects()
    {
        try {
            $rawProjects = $this->projectRepository->fetchAllActiveProjects();
            return $this->processProjectData($rawProjects);
            
        } catch (Exception $e) {
            throw new Exception("Failed to retrieve projects: " . $e->getMessage());
        }
    }
    
    /**
     * Process raw project data from database
     * 
     * Applies business rules and formatting to raw database results:
     * - Decodes JSON location data for map rendering
     * - Converts tag strings to arrays for frontend consumption
     * - Validates and sanitizes data
     * - Applies any business-specific transformations
     * 
     * @param array $rawProjects Raw project data from database
     * @return array Processed project data
     */
    private function processProjectData($rawProjects)
    {
        $processedProjects = [];
        
        foreach ($rawProjects as $project) {
            $processedProject = $this->processIndividualProject($project);
            
            if ($processedProject !== null) {
                $processedProjects[] = $processedProject;
            }
        }
        
        return $processedProjects;
    }
    
    /**
     * Process individual project record
     * 
     * @param array $project Raw project data
     * @return array|null Processed project data or null if invalid
     */
    private function processIndividualProject($project)
    {
        try {
            // Process location data for map rendering
            $project['proj_location'] = $this->processLocationData($project['proj_location']);
            
            // Process tag data for frontend consumption
            $project['tag_names'] = $this->processTagData($project['tag_names']);
            
            // Add computed fields
            $project = $this->addComputedFields($project);
            
            // Validate processed data
            if (!$this->validateProjectData($project)) {
                error_log("Invalid project data for project ID: " . ($project['proj_uniqid'] ?? 'unknown'));
                return null;
            }
            
            return $project;
            
        } catch (Exception $e) {
            error_log("Error processing project: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Process location data for map rendering
     * 
     * @param string|null $locationData Raw location data
     * @return array|string Processed location data
     */
    private function processLocationData($locationData)
    {
        if (empty($locationData)) {
            return '';
        }
        
        // Check if location data is JSON formatted
        if (strpos($locationData, '[') === 0) {
            $decoded = json_decode($locationData, true);
            
            // Return decoded data if valid JSON, otherwise return original
            return ($decoded !== null) ? $decoded : $locationData;
        }
        
        return $locationData;
    }
    
    /**
     * Process tag data for frontend consumption
     * 
     * @param string|null $tagData Raw tag data
     * @return array Processed tag array
     */
    private function processTagData($tagData)
    {
        if (empty($tagData)) {
            return [];
        }
        
        // Convert comma-separated string to array and clean up
        $tags = explode(',', $tagData);
        return array_map('trim', array_filter($tags));
    }
    
    /**
     * Add computed fields to project data
     * 
     * @param array $project Project data
     * @return array Project data with computed fields
     */
    private function addComputedFields($project)
    {
        // Add project manager full name for convenience
        if (!empty($project['proj_mgr_firstname']) || !empty($project['proj_mgr_lastname'])) {
            $project['proj_mgr_fullname'] = trim(
                ($project['proj_mgr_firstname'] ?? '') . ' ' . ($project['proj_mgr_lastname'] ?? '')
            );
        }
        
        // Add project duration if dates are available
        if (!empty($project['proj_startdate']) && !empty($project['proj_enddate'])) {
            $startDate = new DateTime($project['proj_startdate']);
            $endDate = new DateTime($project['proj_enddate']);
            $project['proj_duration_days'] = $startDate->diff($endDate)->days;
        }
        
        // Add subscription status flag for easier frontend handling
        $project['is_subscribed'] = !empty($project['subscription_status']);
        
        return $project;
    }
    
    /**
     * Validate processed project data
     * 
     * @param array $project Project data to validate
     * @return bool True if valid, false otherwise
     */
    private function validateProjectData($project)
    {
        // Basic validation - ensure required fields are present
        $requiredFields = ['proj_uniqid', 'proj_name'];
        
        foreach ($requiredFields as $field) {
            if (empty($project[$field])) {
                return false;
            }
        }
        
        // Validate data types
        if (isset($project['proj_budget']) && !is_numeric($project['proj_budget']) && !empty($project['proj_budget'])) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get project statistics (for future use)
     * 
     * @return array Project statistics
     */
    public function getProjectStatistics()
    {
        // Placeholder for future statistics functionality
        // Could include counts by status, department, etc.
        return [
            'total_active_projects' => 0,
            'projects_by_status' => [],
            'projects_by_department' => []
        ];
    }
}
